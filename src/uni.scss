/**
 * AI穿衣应用全局样式变量
 * 采用清新时尚的配色方案和现代化设计系统
 */

/* 主色调 - 清新薄荷绿系 */
$primary-color: #52C4A0;        // 主色 - 薄荷绿
$primary-light: #7FD4BB;        // 主色浅色
$primary-dark: #3BA986;         // 主色深色
$primary-gradient: linear-gradient(135deg, #52C4A0, #7FD4BB);

/* 辅助色调 */
$secondary-color: #FF8B94;      // 辅助色 - 珊瑚粉
$accent-color: #A8E6CF;         // 强调色 - 浅薄荷
$highlight-color: #FFE5B4;      // 高亮色 - 淡黄

/* 功能色 */
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$info-color: #2196F3;

/* 文字颜色系统 */
$text-primary: #2D3748;         // 主要文字
$text-secondary: #718096;       // 次要文字
$text-tertiary: #A0AEC0;        // 三级文字
$text-disabled: #CBD5E0;        // 禁用文字
$text-white: #FFFFFF;           // 白色文字
$text-dark: #1A202C;           // 深色文字

/* 背景色系统 */
$bg-primary: #FFFFFF;           // 主背景
$bg-secondary: #F7FAFC;         // 次背景
$bg-tertiary: #EDF2F7;          // 三级背景
$bg-card: #FFFFFF;              // 卡片背景
$bg-overlay: rgba(45, 55, 72, 0.5); // 遮罩背景
$bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 边框系统 */
$border-color: #E2E8F0;
$border-light: #F1F5F9;
$border-dark: #CBD5E0;
$border-focus: $primary-color;

/* 阴影系统 */
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

/* 字体系统 */
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-2xl: 48rpx;
$font-size-3xl: 60rpx;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

/* 间距系统 */
$space-xs: 8rpx;
$space-sm: 16rpx;
$space-md: 24rpx;
$space-lg: 32rpx;
$space-xl: 48rpx;
$space-2xl: 64rpx;
$space-3xl: 96rpx;

/* 圆角系统 */
$radius-xs: 4rpx;
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 16rpx;
$radius-xl: 24rpx;
$radius-full: 9999rpx;

/* 图标尺寸 */
$icon-xs: 32rpx;
$icon-sm: 40rpx;
$icon-md: 48rpx;
$icon-lg: 56rpx;
$icon-xl: 64rpx;

/* 动画 */
$transition-fast: 0.15s ease-out;
$transition-base: 0.2s ease-out;
$transition-slow: 0.3s ease-out;

/* Z-index */
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal: 1040;
$z-popover: 1050;
$z-tooltip: 1060;
$z-toast: 1070;

/* 兼容旧版本变量 */
$uni-color-primary: $primary-color;
$uni-color-success: $success-color;
$uni-color-warning: $warning-color;
$uni-color-error: $error-color;

$uni-text-color: $text-primary;
$uni-text-color-inverse: $text-white;
$uni-text-color-grey: $text-secondary;
$uni-text-color-placeholder: $text-tertiary;
$uni-text-color-disable: $text-disabled;

$uni-bg-color: $bg-primary;
$uni-bg-color-grey: $bg-secondary;
$uni-bg-color-hover: $bg-tertiary;
$uni-bg-color-mask: $bg-overlay;

$uni-border-color: $border-color;

$uni-font-size-sm: $font-size-sm;
$uni-font-size-base: $font-size-base;
$uni-font-size-lg: $font-size-lg;

$uni-img-size-sm: $icon-sm;
$uni-img-size-base: $icon-md;
$uni-img-size-lg: $icon-lg;

$uni-border-radius-sm: $radius-sm;
$uni-border-radius-base: $radius-md;
$uni-border-radius-lg: $radius-lg;
$uni-border-radius-circle: $radius-full;

$uni-spacing-row-sm: $space-sm;
$uni-spacing-row-base: $space-md;
$uni-spacing-row-lg: $space-lg;

$uni-spacing-col-sm: $space-xs;
$uni-spacing-col-base: $space-sm;
$uni-spacing-col-lg: $space-md;

$uni-opacity-disabled: 0.3;